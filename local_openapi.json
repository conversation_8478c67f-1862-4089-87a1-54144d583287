{"openapi": "3.1.0", "info": {"title": "Mem0 REST APIs", "description": "A REST API for managing and searching memories for your AI Agents and Apps.", "version": "1.0.0"}, "paths": {"/health": {"get": {"summary": "Health Check", "description": "Health check endpoint for Docker health checks and load balancers.", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/": {"get": {"summary": "Root Endpoint", "description": "Root endpoint that redirects to API documentation.", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/configure": {"post": {"summary": "Configure Mem0", "description": "Set memory configuration.", "operationId": "set_config_configure_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Config"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/memories": {"post": {"summary": "Create memories", "description": "Store new memories.", "operationId": "add_memory_memories_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemoryCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"summary": "Get memories", "description": "Retrieve stored memories.", "operationId": "get_all_memories_memories_get", "parameters": [{"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}}, {"name": "run_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Run Id"}}, {"name": "agent_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Agent Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"summary": "Delete all memories", "description": "Delete all memories for a given identifier.", "operationId": "delete_all_memories_memories_delete", "parameters": [{"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}}, {"name": "run_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Run Id"}}, {"name": "agent_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Agent Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/memories/{memory_id}": {"get": {"summary": "Get a memory", "description": "Retrieve a specific memory by ID.", "operationId": "get_memory_memories__memory_id__get", "parameters": [{"name": "memory_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Memory Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"summary": "Update a memory", "description": "Update an existing memory.", "operationId": "update_memory_memories__memory_id__put", "parameters": [{"name": "memory_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Memory Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Updated Memory"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"summary": "Delete a memory", "description": "Delete a specific memory by ID.", "operationId": "delete_memory_memories__memory_id__delete", "parameters": [{"name": "memory_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Memory Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/search": {"post": {"summary": "Search memories", "description": "Search for memories based on a query.", "operationId": "search_memories_search_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/memories/{memory_id}/history": {"get": {"summary": "Get memory history", "description": "Retrieve memory history.", "operationId": "memory_history_memories__memory_id__history_get", "parameters": [{"name": "memory_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Memory Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/reset": {"post": {"summary": "Reset all memories", "description": "Completely reset stored memories.", "operationId": "reset_memory_reset_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "MemoryCreate": {"properties": {"messages": {"items": {"$ref": "#/components/schemas/Message"}, "type": "array", "title": "Messages", "description": "List of messages to store."}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}, "agent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Agent Id"}, "run_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Run Id"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "includes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Includes", "description": "Include only specific types of memories"}, "excludes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Excludes", "description": "Exclude specific types of memories"}}, "type": "object", "required": ["messages"], "title": "MemoryCreate"}, "Message": {"properties": {"role": {"type": "string", "title": "Role", "description": "Role of the message (user or assistant)."}, "content": {"type": "string", "title": "Content", "description": "Message content."}}, "type": "object", "required": ["role", "content"], "title": "Message"}, "SearchRequest": {"properties": {"query": {"type": "string", "title": "Query", "description": "Search query."}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}, "run_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Run Id"}, "agent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Agent Id"}, "filters": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Filters"}}, "type": "object", "required": ["query"], "title": "SearchRequest"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}